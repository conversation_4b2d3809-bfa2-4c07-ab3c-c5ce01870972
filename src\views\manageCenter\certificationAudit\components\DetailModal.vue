<template>
  <CustomModal
    v-model:open="visible"
    title="认证详情"
    width="800px"
    :show-footer="true"
    :show-cancel-button="false"
    :show-confirm-button="true"
    confirm-text="关闭"
    @confirm="handleCancel"
  >
    <div v-if="record" class="detail-content" v-loading="loading">
      <!-- 企业认证资料 -->
      <template v-if="record.authType === '企业认证'">
        <!-- 企业资料 -->
        <div class="info-section">
          <span class="section-title">企业资料</span>
          <a-descriptions :column="2" bordered size="small" v-if="detailData?.hgyEnterpriseAuth">
            <a-descriptions-item label="企业名称">{{ detailData.hgyEnterpriseAuth.enterpriseName || '-' }}</a-descriptions-item>
            <a-descriptions-item label="统一社会信用代码">{{ detailData.hgyEnterpriseAuth.creditCode || '-' }}</a-descriptions-item>
            <a-descriptions-item label="业务联系人">{{ detailData.hgyEnterpriseAuth.relationUser || '-' }}</a-descriptions-item>
            <a-descriptions-item label="联系电话">{{ detailData.hgyEnterpriseAuth.relationPhone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="营业执照照片" :span="2">
              <div v-if="detailData.hgyEnterpriseAuth.companyLogo" class="image-preview">
                <Image :src="detailData.hgyEnterpriseAuth.companyLogo" :width="200" :preview="true" />
              </div>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 法人信息 -->
        <div class="info-section">
          <span class="section-title">法人信息</span>
          <a-descriptions :column="2" bordered size="small" v-if="detailData?.hgyEnterpriseAuth">
            <a-descriptions-item label="法人真实姓名">{{ detailData.hgyEnterpriseAuth.legalName || '-' }}</a-descriptions-item>
            <a-descriptions-item label="法人证件类型">{{ getCartTypeText(detailData.hgyEnterpriseAuth.cartType) }}</a-descriptions-item>
            <a-descriptions-item label="法人证件号" :span="2">{{ detailData.hgyEnterpriseAuth.cartId || '-' }}</a-descriptions-item>
            <a-descriptions-item label="身份证正反面照片" :span="2">
              <div v-if="detailData.hgyAttachmentList && detailData.hgyAttachmentList.length > 0" class="image-preview">
                <Image
                  v-for="(item, index) in detailData.hgyAttachmentList"
                  :key="index"
                  :src="item.filePath"
                  :width="150"
                  :preview="true"
                  style="margin-right: 10px"
                />
              </div>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </template>

      <!-- 个人认证资料 -->
      <template v-else-if="record.authType === '个人认证'">
        <!-- 个人资料 -->
        <div class="info-section">
          <span class="section-title">个人资料</span>
          <a-descriptions :column="2" bordered size="small" v-if="detailData?.hgyPersonalAuth">
            <a-descriptions-item label="真实姓名">{{ detailData.hgyPersonalAuth.name || '-' }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ detailData.hgyPersonalAuth.phone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="证件类型">{{ getCartTypeText(detailData.hgyPersonalAuth.cartType) }}</a-descriptions-item>
            <a-descriptions-item label="证件号">{{ detailData.hgyPersonalAuth.cartId || '-' }}</a-descriptions-item>
            <a-descriptions-item label="身份证正反面照片" :span="2">
              <div v-if="detailData.hgyAttachmentList && detailData.hgyAttachmentList.length > 0" class="image-preview">
                <Image
                  v-for="(item, index) in detailData.hgyAttachmentList"
                  :key="index"
                  :src="item.filePath"
                  :width="150"
                  :preview="true"
                  style="margin-right: 10px"
                />
              </div>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </template>

      <!-- 审核信息 -->
      <div class="audit-section">
        <span class="section-title">审核信息</span>
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="审核状态">
            <a-tag :color="getReviewStatusColor(record.review)">
              {{ getReviewStatusText(record.review) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="审核人">{{ record.reviewUser || '-' }}</a-descriptions-item>
          <a-descriptions-item label="审核时间">{{ formatToDateTime(dayjs(record.reviewTime)) || '-' }}</a-descriptions-item>
          <a-descriptions-item label="提交时间">{{ formatToDateTime(dayjs(record.submitTime)) || '-' }}</a-descriptions-item>
          <a-descriptions-item label="审核说明" :span="2" v-if="getAuditNotes()">
            <div class="notes-content">{{ getAuditNotes() }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
  </CustomModal>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { getPersonalAuthDetail, getEnterpriseAuthDetail, CertificationAuditRecord } from '/@/api/manageCenter/certificationAudit';
  import CustomModal from '/@/components/Modal/src/CustomModal.vue';
  import dayjs from 'dayjs';
  import { Image } from 'ant-design-vue';

  interface Props {
    open: boolean;
    record: CertificationAuditRecord | null;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const { createMessage } = useMessage();

  const visible = computed({
    get: () => props.open,
    set: (value) => emit('update:open', value),
  });

  const detailData = ref<any>(null);
  const loading = ref(false);

  // 监听弹窗打开，获取详细信息
  watch(
    () => props.open,
    async (newVal) => {
      if (newVal && props.record) {
        await fetchDetailData();
      }
    }
  );

  // 获取详细信息
  async function fetchDetailData() {
    if (!props.record) return;

    try {
      loading.value = true;

      // 根据认证类型调用不同的详情接口
      let result: any;
      if (props.record.authType === '个人认证') {
        result = await getPersonalAuthDetail(props.record.id);
      } else if (props.record.authType === '企业认证') {
        result = await getEnterpriseAuthDetail(props.record.id);
      } else {
        createMessage.error('未知的认证类型');
        return;
      }

      detailData.value = result;
    } catch (error) {
      console.error('获取详情失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 取消操作
  function handleCancel() {
    visible.value = false;
    detailData.value = null;
  }

  // 获取审核状态颜色
  function getReviewStatusColor(status: number): string {
    switch (status) {
      case 1:
        return 'orange'; // 未审核
      case 2:
        return 'green'; // 已通过
      case 3:
        return 'red'; // 未通过
      default:
        return 'default';
    }
  }

  // 获取审核状态文本
  function getReviewStatusText(status: number): string {
    switch (status) {
      case 1:
        return '未审核';
      case 2:
        return '已通过';
      case 3:
        return '未通过';
      default:
        return '-';
    }
  }

  // 获取证件类型文本
  function getCartTypeText(type: number): string {
    switch (type) {
      case 1:
        return '身份证';
      case 2:
        return '其他';
      default:
        return '-';
    }
  }

  // 获取审核说明
  function getAuditNotes(): string {
    if (!detailData.value) return '';

    if (props.record?.authType === '企业认证') {
      return detailData.value.hgyEnterpriseAuth?.notes || '';
    } else if (props.record?.authType === '个人认证') {
      return detailData.value.hgyPersonalAuth?.notes || '';
    }

    return '';
  }
</script>

<style lang="less" scoped>
  .detail-content {
    padding-top: 20px;

    .info-section,
    .audit-section {
      margin-bottom: 12px;

      .section-title {
        display: block;
        margin-bottom: 10px;
        font-size: 16px;
        font-family: 'PingFang Bold';
        color: #262626;
        border-left: 4px solid #004c66;
        padding-left: 12px;
      }
    }

    .audit-section {
      border-top: 1px solid #f0f0f0;
      padding-top: 24px;
    }

    .image-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      :deep(.ant-image) {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        overflow: hidden;
      }
    }

    .notes-content {
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
      padding: 8px 12px;
      background-color: #fafafa;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
    }
  }
</style>
