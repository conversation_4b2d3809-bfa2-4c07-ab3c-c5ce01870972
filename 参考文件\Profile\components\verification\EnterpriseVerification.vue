<template>
  <div class="enterprise-verification">
    <!-- 企业认证表单 -->
    <div class="form-section">
      <!-- 企业名称 -->
      <div class="form-item">
        <label class="form-label">企业名称</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.companyName"
            type="text"
            class="form-input"
            placeholder="请输入企业名称"
          />
        </div>
      </div>

      <!-- 统一社会信用代码 -->
      <div class="form-item">
        <label class="form-label">统一社会信用代码</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.creditCode"
            type="text"
            class="form-input"
            placeholder="请输入统一社会信用代码"
          />
        </div>
      </div>

      <!-- 业务联系人 -->
      <div class="form-item">
        <label class="form-label">业务联系人</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.contactPerson"
            type="text"
            class="form-input"
            placeholder="请输入业务联系人"
          />
        </div>
      </div>

      <!-- 联系电话 -->
      <div class="form-item">
        <label class="form-label">联系电话</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.contactPhone"
            type="text"
            class="form-input"
            placeholder="请输入联系电话"
          />
        </div>
      </div>

      <!-- 公司地址 -->
      <div class="form-item">
        <label class="form-label">公司地址</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.address"
            type="text"
            class="form-input"
            placeholder="请输入公司地址"
          />
        </div>
      </div>

      <!-- 开票电话 -->
      <div class="form-item">
        <label class="form-label">开票电话</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.kpdianhua"
            type="text"
            class="form-input"
            placeholder="请输入开票电话"
          />
        </div>
      </div>

      <!-- 开户行 -->
      <div class="form-item">
        <label class="form-label">开户行</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.kaihuhang"
            type="text"
            class="form-input"
            placeholder="请输入开户行"
          />
        </div>
      </div>

      <!-- 公司账户 -->
      <div class="form-item">
        <label class="form-label">公司账户</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.gonghu"
            type="text"
            class="form-input"
            placeholder="请输入公司账户"
          />
        </div>
      </div>

      <!-- 行号 -->
      <div class="form-item">
        <label class="form-label">行号</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.hanghao"
            type="text"
            class="form-input"
            placeholder="请输入行号"
          />
        </div>
      </div>

      <!-- 回收种类 -->
      <div class="form-item">
        <label class="form-label">回收种类</label>
        <div class="form-value">
          <el-checkbox-group
            v-model="enterpriseForm.biaoqian"
            class="checkbox-group"
          >
            <el-checkbox
              v-for="option in recycleOptions"
              :key="option.value"
              :value="option.value"
              class="checkbox-item"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>

      <!-- 所属地区 -->
      <div class="form-item">
        <label class="form-label">所属地区</label>
        <div class="form-value">
          <el-cascader
            v-model="enterpriseForm.selectedArea"
            :options="areaOptions"
            :props="{ expandTrigger: 'hover' }"
            placeholder="请选择省市区"
            class="area-cascader"
            @change="handleAreaChange"
          />
        </div>
      </div>

      <!-- 营业执照 -->
      <div class="form-item">
        <label class="form-label">营业执照</label>
        <div class="form-value">
          <div class="upload-section">
            <p class="upload-description">
              请上传营业执照副本，要求为彩色，最多可上传1个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。
            </p>
            <UploadCard
              uploadText="营业执照"
              backgroundImage="@/assets/icons/upload/business-license.svg"
              :imageUrl="enterpriseForm.businessLicenseUrl"
              @upload="handleBusinessLicenseUpload"
            />
          </div>
        </div>
      </div>

      <!-- 法人姓名 -->
      <div class="form-item">
        <label class="form-label">法人姓名</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.legalPersonName"
            type="text"
            class="form-input"
            placeholder="请输入法人姓名"
          />
        </div>
      </div>

      <!-- 法人身份证号 -->
      <div class="form-item">
        <label class="form-label">法人身份证号</label>
        <div class="form-value">
          <input
            v-model="enterpriseForm.legalPersonIdCard"
            type="text"
            class="form-input"
            placeholder="请输入法人身份证号"
          />
        </div>
      </div>

      <!-- 上传法人证件 -->
      <div class="form-item">
        <label class="form-label">上传法人证件</label>
        <div class="form-value">
          <div class="upload-section">
            <p class="upload-description">
              请上传法人证件，要求为彩色，最多可上传2个附件，单文件最大5MB，类型支持gif、png、jpg、bmp。
            </p>
            <div class="upload-group">
              <UploadCard
                uploadText="身份证正面"
                backgroundImage="@/assets/icons/upload/idcard-front.svg"
                :imageUrl="enterpriseForm.legalPersonFrontUrl"
                @upload="handleLegalPersonFrontUpload"
              />
              <UploadCard
                uploadText="身份证反面"
                backgroundImage="@/assets/icons/upload/idcard-reverse.svg"
                :imageUrl="enterpriseForm.legalPersonBackUrl"
                @upload="handleLegalPersonBackUpload"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, defineExpose } from "vue";
import { ElMessage } from "element-plus";
import UploadCard from "@/components/UploadCard.vue";
import { userApi } from "@/utils/api";
import { uploadToOSS, validateFile } from "./uploadUtils";
import { useUserStore } from "@/stores/user";
import {
  initAreaData,
  getAreaCodesByNames,
  getAreaNamesByCodes,
  type AreaOption,
} from "./areaUtils";

// Props
interface Props {
  areaOptions: AreaOption[];
  recycleOptions: Array<{ value: string; label: string }>;
}

defineProps<Props>();

// 定义组件的 emits
interface Emits {
  (e: "save", data: any): void;
  (e: "submit", data: any): void;
}

const emit = defineEmits<Emits>();

// 用户状态管理
const userStore = useUserStore();

// 企业认证表单数据
const enterpriseForm = reactive({
  companyName: "", // 企业名称
  creditCode: "", // 统一社会信用代码
  contactPerson: "", // 业务联系人
  contactPhone: "", // 联系电话
  address: "", // 公司地址
  kpdianhua: "", // 开票电话
  kaihuhang: "", // 开户行
  gonghu: "", // 公司账户
  hanghao: "", // 行号
  biaoqian: [] as string[], // 回收种类
  selectedArea: [] as string[], // 所属地区选择器值
  province: "", // 省
  city: "", // 市
  district: "", // 区
  legalPersonName: "", // 法人姓名
  legalPersonIdCard: "", // 法人身份证号
  businessLicense: null as File | null, // 营业执照文件
  businessLicenseUrl: "", // 营业执照URL
  legalPersonFront: null as File | null, // 法人身份证正面
  legalPersonFrontUrl: "", // 法人身份证正面URL
  legalPersonBack: null as File | null, // 法人身份证反面
  legalPersonBackUrl: "", // 法人身份证反面URL
  member_id: userStore.userInfo ? userStore.userInfo.id : 0, // 会员ID
});

/**
 * 处理营业执照上传
 * @param file 上传的文件
 */
const handleBusinessLicenseUpload = async (file: File) => {
  try {
    // 验证文件
    if (!validateFile(file)) {
      return;
    }

    enterpriseForm.businessLicense = file;
    const url = await uploadToOSS(file);
    enterpriseForm.businessLicenseUrl = url;
    ElMessage.success("营业执照上传成功");
  } catch (error) {
    ElMessage.error("营业执照上传失败");
  }
};

/**
 * 处理法人身份证正面上传
 * @param file 上传的文件
 */
const handleLegalPersonFrontUpload = async (file: File) => {
  try {
    // 验证文件
    if (!validateFile(file)) {
      return;
    }

    enterpriseForm.legalPersonFront = file;
    const url = await uploadToOSS(file);
    enterpriseForm.legalPersonFrontUrl = url;
    ElMessage.success("法人身份证正面上传成功");
  } catch (error) {
    ElMessage.error("法人身份证正面上传失败");
  }
};

/**
 * 处理法人身份证反面上传
 * @param file 上传的文件
 */
const handleLegalPersonBackUpload = async (file: File) => {
  try {
    // 验证文件
    if (!validateFile(file)) {
      return;
    }

    enterpriseForm.legalPersonBack = file;
    const url = await uploadToOSS(file);
    enterpriseForm.legalPersonBackUrl = url;
    ElMessage.success("法人身份证反面上传成功");
  } catch (error) {
    ElMessage.error("法人身份证反面上传失败");
  }
};

/**
 * 处理地区变化
 * @param value 选择的地区值数组
 */
const handleAreaChange = (value: string[]) => {
  if (value && value.length === 3) {
    enterpriseForm.province = value[0];
    enterpriseForm.city = value[1];
    enterpriseForm.district = value[2];
  } else {
    enterpriseForm.province = "";
    enterpriseForm.city = "";
    enterpriseForm.district = "";
  }
};

/**
 * 处理保存操作
 */
const handleSave = async () => {
  try {
    // 验证企业认证表单
    if (!enterpriseForm.companyName || !enterpriseForm.creditCode) {
      ElMessage.warning("请填写完整的企业信息");
      return;
    }

    const submitData = {
      qiyemingcheng: enterpriseForm.companyName,
      xinyongdaima: enterpriseForm.creditCode,
      lianxiren: enterpriseForm.contactPerson,
      lianxidianhua: enterpriseForm.contactPhone,
      address: enterpriseForm.address,
      kpdianhua: enterpriseForm.kpdianhua,
      kaihuhang: enterpriseForm.kaihuhang,
      gonghu: enterpriseForm.gonghu,
      hanghao: enterpriseForm.hanghao,
      biaoqian: enterpriseForm.biaoqian.join(","),
      province: enterpriseForm.province,
      city: enterpriseForm.city,
      district: enterpriseForm.district,
      fr_name: enterpriseForm.legalPersonName,
      fr_cardnum: enterpriseForm.legalPersonIdCard,
      qiyepic: enterpriseForm.businessLicenseUrl,
      fr_cardpicz: enterpriseForm.legalPersonFrontUrl,
      fr_cardpicf: enterpriseForm.legalPersonBackUrl,
      status: 2, // 保存
      type: 2, // 企业认证
      member_id: userStore.userInfo?.id || 0,
    };

    emit("save", submitData);
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

/**
 * 处理提交审核操作
 */
const handleSubmit = async () => {
  try {
    // 验证企业认证表单
    if (
      !enterpriseForm.companyName ||
      !enterpriseForm.creditCode ||
      !enterpriseForm.businessLicenseUrl ||
      !enterpriseForm.legalPersonFrontUrl ||
      !enterpriseForm.legalPersonBackUrl
    ) {
      ElMessage.warning("请填写完整的企业信息并上传相关证件");
      return;
    }

    const submitData = {
      qiyemingcheng: enterpriseForm.companyName,
      xinyongdaima: enterpriseForm.creditCode,
      lianxiren: enterpriseForm.contactPerson,
      lianxidianhua: enterpriseForm.contactPhone,
      address: enterpriseForm.address,
      kpdianhua: enterpriseForm.kpdianhua,
      kaihuhang: enterpriseForm.kaihuhang,
      gonghu: enterpriseForm.gonghu,
      hanghao: enterpriseForm.hanghao,
      biaoqian: enterpriseForm.biaoqian.join(","),
      province: enterpriseForm.province,
      city: enterpriseForm.city,
      district: enterpriseForm.district,
      fr_name: enterpriseForm.legalPersonName,
      fr_cardnum: enterpriseForm.legalPersonIdCard,
      qiyepic: enterpriseForm.businessLicenseUrl,
      fr_cardpicz: enterpriseForm.legalPersonFrontUrl,
      fr_cardpicf: enterpriseForm.legalPersonBackUrl,
      status: 3, // 提交
      type: 2, // 企业认证
      member_id: userStore.userInfo?.id || 0,
    };

    emit("submit", submitData);
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

/**
 * 加载企业认证信息
 */
const loadEnterpriseCertificationInfo = async () => {
  try {
    const memberId = userStore.userInfo?.id;
    if (!memberId) {
      console.warn("用户ID不存在");
      return;
    }

    // 获取企业认证信息
    const enterpriseResult = await userApi.getCertificationInfo({
      member_id: memberId,
      type: 2,
    });

    if (enterpriseResult.code === 1 && enterpriseResult.data) {
      const data = enterpriseResult.data;
      console.log("data", data);

      // 回显企业认证数据
      enterpriseForm.companyName = data.qiyemingcheng || "";
      enterpriseForm.creditCode = data.xinyongdaima || "";
      enterpriseForm.contactPerson = data.lianxiren || "";
      enterpriseForm.contactPhone = data.lianxidianhua || "";
      enterpriseForm.address = data.address || "";
      enterpriseForm.kpdianhua = data.kpdianhua || "";
      enterpriseForm.kaihuhang = data.kaihuhang || "";
      enterpriseForm.gonghu = data.gonghu || "";
      enterpriseForm.hanghao = data.hanghao || "";

      console.log("biaoqian", data.biaoqian.split(","));

      // 回收种类回显
      if (data.biaoqian) {
        enterpriseForm.biaoqian = data.biaoqian.split(",");
      }

      enterpriseForm.legalPersonName = data.fr_name || "";
      enterpriseForm.legalPersonIdCard = data.fr_cardnum || "";
      enterpriseForm.businessLicenseUrl = data.qiyepic || "";
      enterpriseForm.legalPersonFrontUrl = data.fr_cardpicz || "";
      enterpriseForm.legalPersonBackUrl = data.fr_cardpicf || "";

      // 地区回显
      enterpriseForm.selectedArea = [
        data.province.toString(),
        data.city.toString(),
        data.district.toString(),
      ];
    }
  } catch (error) {
    console.error("加载企业认证信息失败:", error);
  }
};

// 暴露方法给父组件
defineExpose({
  handleSave,
  handleSubmit,
});

// 组件挂载时加载认证信息
onMounted(() => {
  loadEnterpriseCertificationInfo();
});
</script>

<style scoped lang="scss">
.enterprise-verification {
  .form-section {
    .form-item {
      padding: 20px 0;
      border-bottom: 1px solid;
      // 渐变
      border-image: linear-gradient(
        to right,
        rgba(221, 221, 221, 1),
        rgba(255, 255, 255, 1)
      );
      border-image-slice: 1;

      .form-label {
        width: 120px;
        font-size: 16px;
        color: #333;
        flex-shrink: 0;
        font-family: "PingFang Bold";
      }

      .form-value {
        margin-top: 10px;
        flex: 1;

        .form-input {
          width: 100%;
          border: none; // 输入框没有边框
          outline: none; // 去除聚焦时的边框
          font-size: 16px;
          color: #333;
          background: transparent;
          padding: 8px 0;

          &::placeholder {
            color: #999;
          }
        }

        .upload-section {
          .upload-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
          }

          .upload-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
          }
        }
      }
    }
  }
}
</style>
