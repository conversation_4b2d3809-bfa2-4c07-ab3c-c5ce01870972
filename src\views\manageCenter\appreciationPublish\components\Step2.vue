<template>
  <div class="step2">
    <a-form :model="formData.contact" :rules="rules" ref="formRef" :scroll-to-first-error="true">
      <!-- 联系人信息板块 -->
      <div class="form-section">
        <div class="section-title">联系人信息</div>
        <div class="section-content">
          <div class="form-row contact-row">
            <a-form-item label="联系人姓名" name="contactName" required class="contact-item">
              <a-input v-model:value="formData.contact.contactName" placeholder="请输入联系人姓名" size="large" />
            </a-form-item>
            <a-form-item label="联系电话" name="contactPhone" required class="contact-item">
              <a-input v-model:value="formData.contact.contactPhone" placeholder="请输入联系电话" :maxlength="11" size="large" />
            </a-form-item>
            <!-- 占位符 -->
            <a-form-item class="contact-item"> </a-form-item>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch, computed, ref } from 'vue';

  // Props 定义
  interface Props {
    modelValue: {
      contact: {
        contactName: string;
        contactPhone: string;
      };
    };
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 表单数据
  const formData = reactive({ ...props.modelValue });

  // 监听表单数据变化
  watch(
    formData,
    (newVal) => {
      emit('update:modelValue', { ...newVal });
    },
    { deep: true }
  );

  // 监听 props 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      Object.assign(formData, newVal);
    },
    { deep: true }
  );

  // 表单验证规则
  const rules = computed(() => ({
    contactName: [
      { required: true, message: '请输入联系人姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '联系人姓名长度应在2-20个字符之间', trigger: 'blur' },
    ],
    contactPhone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
  }));

  // 表单引用
  const formRef = ref();

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 暴露验证方法
  defineExpose({
    validateForm,
  });
</script>

<style lang="less" scoped>
  .step2 {
    .form-section {
      margin-bottom: 32px;
    }

    // 联系信息行布局
    .contact-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .contact-item {
      flex: 1;
      min-width: 200px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-bottom: 8px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 18px;
        margin-right: 8px;
        background-color: #004c66;
      }
    }

    // 表单项样式调整
    :deep(.ant-form-item) {
      margin-bottom: 16px;
      align-items: flex-start;

      .ant-form-item-label {
        text-align: left;
        width: auto;
        min-width: 90px;
        padding-right: 0;
        display: flex;
        align-items: center;
        height: 40px;

        label {
          color: #666;
          font-size: 16px;
          font-weight: 400;
          line-height: 1;

          &::after {
            content: '';
            margin: 0;
          }
        }
      }

      .ant-form-item-control {
        flex: 1;
        margin-left: 10px;
      }

      .ant-form-item-control-input {
        min-height: 40px;
      }
    }

    // 输入框样式
    :deep(.ant-input) {
      border-radius: 6px;
    }
  }
</style>
