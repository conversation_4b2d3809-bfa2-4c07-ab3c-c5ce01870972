<template>
  <div class="followed-companies">
    <!-- 顶部：关注企业数量显示 -->
    <div class="header-section">
      <div class="follow-count">
        <span class="count">{{ followedCompanies.length }}</span>
        <span class="text">关注企业</span>
      </div>
    </div>

    <!-- 中间：搜索框 -->
    <!-- <div class="search-section">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索企业名称"
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-button class="search-btn" @click="handleSearch">
              <template #default>
                <SvgIcon iconName="support-search" className="search-icon" />
              </template>
            </el-button>
          </template>
        </el-input>
      </div>
    </div> -->

    <!-- 下方：关注企业列表 -->
    <div class="companies-list">
      <div
        class="company-item"
        v-for="(company, index) in followedCompanies"
        :key="company.id"
        :class="{ 'no-border': index === followedCompanies.length - 1 }"
      >
        <!-- 左边：企业图标 -->
        <div class="company-avatar">
          <img
            :src="`https://huigupaimai.oss-cn-beijing.aliyuncs.com/${company.qiye.qiyelogo}`"
            :alt="company.qiye.qiyemingcheng"
            class="avatar-img"
          />
        </div>

        <!-- 中间：企业信息 -->
        <div class="company-info">
          <div class="company-name">{{ company.qiye.qiyemingcheng }}</div>
          <div class="company-description">{{ company.qiye.qiyephone }}</div>
        </div>

        <!-- 右边：已关注按钮 -->
        <button class="followed-btn" @click="handleUnfollow(company.qiye_id)">
          取消关注
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElInput, ElButton, ElMessageBox, ElMessage } from "element-plus";
import SvgIcon from "@/components/SvgIcon.vue";
import { userApi, companyApi } from "@/utils/api";
import { useUserStore } from "@/stores/user";

// 获取用户数据
const userStore = useUserStore();

// 响应式数据
const searchKeyword = ref("");

// 模拟关注企业数据
const followedCompanies = ref<any>([]);

// 计算过滤后的企业列表
/* const filteredCompanies = computed(() => {
  if (!searchKeyword.value.trim()) {
    return followedCompanies.value;
  }
  return followedCompanies.value.filter(
    (company) =>
      company.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      company.description
        .toLowerCase()
        .includes(searchKeyword.value.toLowerCase())
  );
}); */

// 组件挂载完成
onMounted(() => {
  getFollowedCompanies();
});

// 获取关注的企业
const getFollowedCompanies = async () => {
  const res = await userApi.getMyFavoriteCompanies({
    member_id: userStore.userInfo?.id as number,
    page: 1,
    type: 1,
  });

  if (res.code === 1) {
    followedCompanies.value = res.data.data;
  }
};

// 处理搜索
const handleSearch = () => {
  console.log("搜索关键词:", searchKeyword.value);
};

// 处理取消关注
const handleUnfollow = async (companyId: number) => {
  // 询问用户是否要取消关注
  const confirm = await ElMessageBox.confirm("确定取消关注吗？", "取消关注", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  if (confirm) {
    const res = await companyApi.favoriteCompany({
      member_id: userStore.userInfo?.id as number,
      qiye_id: companyId,
      isquxiao: 0,
      type: 1,
    });
    if (res.code === 1) {
      ElMessage.success("取消关注成功");
      // 刷新列表
      getFollowedCompanies();
    }
  }
};
</script>

<style lang="scss" scoped>
.followed-companies {
  margin: 30px 20px 20px 20px;
}

// 顶部关注数量显示
.header-section {
  margin-bottom: 20px;

  .follow-count {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    .count {
      font-family: "DIN";
      font-size: 38px;
      margin-right: 5px;
    }
  }
}

// 中间搜索框样式
.search-section {
  margin-bottom: 20px;

  .search-box {
    max-width: 100%;

    .search-input {
      :deep(.el-input__wrapper) {
        border-radius: 6px;
        padding: 0 2px 0 20px;
        height: 50px;
        box-shadow: 0 0 0 rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid #ddd;
      }

      :deep(.el-input__inner) {
        font-size: 16px;
        padding-right: 50px;
        color: #ddd;

        &::placeholder {
          font-size: 16px;
          color: #ddd;
        }
      }

      .search-btn {
        border-radius: 6px;
        width: 70px;
        height: 44px;
        padding: 0;
        border: none;
        background: #dddddd;

        &:hover {
          background: #003d52;
        }

        .search-icon {
          width: 17px;
          height: 18px;
          color: #8f9190;
        }
      }
    }
  }
}

// 企业列表样式
.companies-list {
  .company-item {
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid;
    // 渐变分割线样式，参考AccountSecurity
    border-image: linear-gradient(
      to right,
      rgba(221, 221, 221, 1),
      rgba(255, 255, 255, 1)
    );
    border-image-slice: 1;

    &.no-border {
      border-bottom: none;
    }

    // 左边企业图标
    .company-avatar {
      margin-right: 16px;
      width: 72px;
      height: 72px;
      border-radius: 50%;
      border: 2px solid #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;

      .avatar-img {
        width: 41.39px;
        height: 48.46px;
        object-fit: cover;
      }
    }

    // 中间企业信息
    .company-info {
      flex: 1;
      margin-right: 16px;

      .company-name {
        font-size: 18px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 500;
        line-height: 1.4;
      }

      .company-description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    // 右边已关注按钮
    .followed-btn {
      padding: 8px 24px;
      background: #e6eef0;
      border: 1px solid #004c66;
      border-radius: 6px;
      color: #004c66;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s;
      white-space: nowrap;

      &:hover {
        background: rgba(0, 76, 102, 0.1);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .followed-companies {
    margin: 20px 15px;
  }

  .header-section {
    .follow-count {
      font-size: 20px;
    }
  }

  .search-section {
    .search-box {
      width: 100%;

      .search-input {
        :deep(.el-input__wrapper) {
          height: 44px;
        }
      }
    }
  }

  .companies-list {
    .company-item {
      .company-avatar {
        margin-right: 12px;

        .avatar-img {
          width: 60px;
          height: 60px;
        }
      }

      .company-info {
        margin-right: 12px;

        .company-name {
          font-size: 16px;
        }

        .company-description {
          font-size: 13px;
        }
      }

      .followed-btn {
        padding: 6px 16px;
        font-size: 13px;
      }
    }
  }
}
</style>
