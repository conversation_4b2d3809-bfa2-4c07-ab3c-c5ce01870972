# 采购信息页面

## 概述

采购信息页面是基于委托处置页面样式创建的，用于显示和管理采购相关信息。

## 文件结构

```
src/views/orderManage/procurementInfo/
├── index.vue                    # 主页面组件
└── README.md                   # 说明文档

src/api/orderManage/
└── procurementInfo.ts          # API接口文件
```

## 功能特性

### 1. 页面布局

- 基于 JeecgBoot 的 BasicTable 组件
- 支持导航栏切换（全部采购、草稿、待审核、已通过、未通过）
- 支持搜索表单筛选
- 支持数据导出功能

### 2. 显示字段

根据老版本采购信息页面的字段配置，显示以下信息：

- 序号
- 服务单ID (entrustOrderId)
- 公告名称 (noticeName)
- 省份 (province)
- 城市 (city)
- 区县 (district)
- 详细地址 (address)
- 特殊说明 (specialNotes)
- 审核状态 (status)：1-待审核，2-审核通过，3-审核拒绝
- 委托方式 (entrustType)：1-自主，2-增值
- 创建时间 (createTime)
- 更新时间 (updateTime)
- 创建人 (createBy)
- 更新人 (updateBy)

### 3. 筛选条件

搜索表单支持以下筛选条件：

- 服务单ID
- 公告名称
- 省份
- 城市

### 4. 操作功能

- 查看详情：调用API获取详细信息，支持审核状态显示
- 删除：删除采购信息记录，使用确认弹窗
- 导出：支持按条件导出数据到Excel

### 5. 导航状态

支持按状态筛选：

- 全部采购：显示所有记录
- 待审核：status = 1
- 审核通过：status = 2
- 审核拒绝：status = 3

## API接口

### 接口地址

基于老版本采购信息的API接口：

- 列表查询：`/hgy/entrustService/hgyProcurement/list`
- 详情查询：`/hgy/entrustService/hgyProcurement/queryProcurementById`
- 删除：`/hgy/entrustService/hgyProcurement/customProcurementDelete`
- 导出：`/hgy/entrustService/hgyProcurement/customProcurementExportXls`

### 请求参数

- entrustType: 1 (委托类型)
- serviceType: 3 (采购服务)
- 其他筛选参数根据搜索表单动态添加

## 样式说明

- 页面样式参考委托处置页面 (`src/views/orderManage/entrustDispose/index.vue`)
- 使用相同的布局结构和样式配置
- 支持响应式设计和表格自适应

## 状态管理

- 支持审核状态显示和颜色标识
- 审核状态映射：
  - 1: 待审核 (processing)
  - 2: 审核通过 (success)
  - 3: 审核拒绝 (error)
- 委托方式映射：
  - 1: 自主 (blue)
  - 2: 增值 (orange)

## 使用说明

1. 页面会自动加载采购信息列表
2. 可通过顶部导航栏切换不同状态的记录
3. 使用搜索表单进行条件筛选
4. 点击"查看详情"按钮查看完整信息
5. 点击"删除"按钮删除记录（需要确认）
6. 使用导航栏中的导出按钮导出数据

## 注意事项

- 编辑功能已暂时隐藏
- 删除操作需要确认，使用自定义删除接口
- 详情查看会调用专门的详情接口获取完整数据
- 导出功能使用自定义导出接口
- 时间字段会自动格式化显示
