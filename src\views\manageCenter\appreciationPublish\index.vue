<template>
  <div class="appreciation-publish">
    <!-- 步骤条 -->
    <div class="steps-wrapper">
      <Steps :current="currentStep" :steps="stepsList" @change="handleStepChange" />
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：编辑委托信息 -->
      <div v-if="currentStep === 0">
        <Step1
          ref="step1Ref"
          v-model="stepOneData"
          :location-loading="locationLoading"
          :is-edit-mode="isEditMode"
          @area-change="handleAreaChange"
          @get-current-location="getCurrentLocation"
          @service-type-change="handleServiceTypeChange"
        />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <a-button type="primary" size="large" class="next-btn" @click="nextStep"> 下一步 </a-button>
        </div>
      </div>

      <!-- 第二步：联系信息 -->
      <div v-if="currentStep === 1">
        <Step2 ref="step2Ref" v-model="stepTwoData" />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <!-- 修改模式下只显示保存修改按钮 -->
          <template v-if="isEditMode">
            <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 保存修改 </a-button>
          </template>
          <!-- 新增模式下显示原有按钮 -->
          <template v-else>
            <a-button size="large" class="draft-btn" @click="submitForm(1)"> 保存至草稿 </a-button>
            <a-button type="primary" size="large" class="submit-btn" :loading="submitting" @click="submitForm(2)"> 确认发布 </a-button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, computed, onMounted, watch, provide } from 'vue';
  import { message } from 'ant-design-vue';
  import { useRouter, useRoute } from 'vue-router';
  import Steps from '@/components/Steps/index.vue';
  import Step1 from './components/Step1.vue';
  import Step2 from './components/Step2.vue';
  // 引入API接口
  import { queryPassedReview, platformUpdate } from '@/api/manageCenter/appreciationPublish';
  import type { PassedReviewItem, PlatformUpdateParams } from '@/api/manageCenter/appreciationPublish';

  // 组件引用
  const step1Ref = ref<InstanceType<typeof Step1>>();
  const step2Ref = ref<InstanceType<typeof Step2>>();

  // 路由实例
  const router = useRouter();
  const route = useRoute();

  // 当前步骤
  const currentStep = ref(0);

  // 服务类型 (2-发布资产处置 3-发布采购信息)
  const serviceType = ref(2);

  // 提交状态
  const submitting = ref(false);

  // 定位加载状态
  const locationLoading = ref(false);

  // 编辑状态
  const isEditMode = ref(false);
  const editId = ref<string>('');

  // 委托单列表
  const entrustOrderList = ref<PassedReviewItem[]>([]);
  // 委托单加载状态
  const entrustOrderLoading = ref(false);

  // 提供委托单列表数据给子组件
  provide('entrustOrderList', entrustOrderList);
  provide('entrustOrderLoading', entrustOrderLoading);

  // 动态步骤列表
  const stepsList = computed(() => {
    return [
      {
        title: '编辑委托信息',
        description: '填写委托的基本信息',
      },
      {
        title: '发布委托信息',
        description: '填写联系人信息并发布',
      },
    ];
  });

  // 第一步表单数据
  const stepOneData = reactive({
    // 服务类型
    serviceType: 2,
    // 关联委托单号
    entrustOrderId: '',
    // 委托信息
    entrustInfo: {
      title: '', // 委托单位（显示企业名称）
      type: '', // 受委托单位（显示企业名称）
      description: '', // 委托描述
      noticeName: '', // 公告名称（仅用于采购信息）
    },
    // 基本信息
    basicInfo: {
      // 发布资产处置
      entrustCompanyId: '', // 处置单位ID
      assetName: '', // 资产名称
      assetNo: '', // 资产编号
      assetType: 1, // 资产类型
      quantity: '', // 资产数量
      unit: '台', // 计量单位
      quantityFlag: 0, // 是否展示实际数量 0-否 1-是
      serviceLife: 0, // 使用年限
      depreciationDegree: 1, // 新旧程度(1-九成新 2-八成新...)
      currentStatus: 1, // 当前状态(1-在用 2-闲置 3-报废)
      appraisalValue: 0, // 评估价值
      disposalPrice: 0, // 处置底价
      disposalStartTime: '', // 处置开始时间
      disposalEndTime: '', // 处置结束时间
      paymentMethod: 1, // 付款方式(1-全款 2-分期)
      isTaxIncluded: '0', // 是否含税(0:表示不含税,other：表示含税率)
      taxRate: 0, // 税点
    },
    // 存放位置
    location: {
      province: '110000', // 省份
      city: '110100', // 城市
      area: '110101', // 区域
      detailAddress: '', // 详细地址
      coordinates: {
        // 坐标信息
        latitude: '',
        longitude: '',
      },
    },
    // 资料上传
    materials: {
      images: [] as any[] | string, // 资产图片
      attachments: [] as any[] | string, // 附件上传
      specialNote: '', // 特殊说明
    },
    other: {
      // 直接字段（与Step1组件Props接口匹配）
      images: [] as any[] | string, // 资产图片
      attachments: [] as any[] | string, // 附件上传
      entrustDocument: [], // 委托单上传
      specialNote: '', // 特殊说明
    },
    // 附件列表（用于回显）
    hgyAttachmentList: [] as any[],
  });

  // 第二步表单数据（联系人信息）
  const stepTwoData = reactive({
    contact: {
      contactName: '', // 联系人姓名
      contactPhone: '', // 联系电话
    },
  });

  watch(
    stepOneData,
    (newVal) => {
      console.log('stepOneData变化:', newVal);
    },
    { deep: true }
  );

  // 处理步骤变化
  const handleStepChange = (step: number) => {
    // 只允许点击当前步骤之前的步骤返回，当前步骤之后的步骤不能通过点击步骤条跳转
    if (step < currentStep.value) {
      currentStep.value = step;
    }
  };

  // 省市区变化处理
  const handleAreaChange = (value: any) => {
    console.log('省市区变化:', value);
  };

  // 获取当前位置
  const getCurrentLocation = () => {
    locationLoading.value = true;

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // 模拟根据经纬度获取地址信息
          setTimeout(() => {
            stepOneData.location.detailAddress = '模拟获取的详细地址：北京市海淀区中关村大街1号';
            locationLoading.value = false;
            message.success('位置获取成功');
          }, 1000);
        },
        (error) => {
          locationLoading.value = false;
          message.error('位置获取失败，请手动输入地址');
        }
      );
    } else {
      locationLoading.value = false;
      message.error('浏览器不支持地理位置获取');
    }
  };

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    serviceType.value = value;
    // 重置步骤
    currentStep.value = 0;
    // 更新stepOneData中的服务类型，确保数据同步
    stepOneData.serviceType = value;
  };

  // 获取已通过审核的委托单列表
  const fetchEntrustOrderList = async () => {
    try {
      entrustOrderLoading.value = true;
      const result = await queryPassedReview();
      console.log('获取委托单列表:', result);

      if (result && Array.isArray(result)) {
        entrustOrderList.value = result;
      }
    } catch (error) {
      console.error('获取委托单列表失败:', error);
      message.error('获取委托单列表失败');
    } finally {
      entrustOrderLoading.value = false;
    }
  };

  // 组件挂载时获取委托单列表和处理编辑模式
  onMounted(() => {
    fetchEntrustOrderList();

    // 检查是否为编辑模式
    const id = route.query.id as string;
    const serviceTypeParam = route.query.serviceType as string;

    if (id) {
      isEditMode.value = true;
      editId.value = id;

      // 设置服务类型
      if (serviceTypeParam) {
        const serviceTypeValue = parseInt(serviceTypeParam);
        serviceType.value = serviceTypeValue;
        stepOneData.serviceType = serviceTypeValue;
      }

      // 如果是编辑模式，获取详情数据
      // fetchEntrustDetail(id);
    }
  });

  // 下一步
  const nextStep = async () => {
    // 验证当前步骤的表单数据
    if (currentStep.value === 0) {
      // 第一步：验证基本信息
      const isValid = await step1Ref.value?.validateForm();
      if (!isValid) {
        return;
      }
    }
    console.log('验证通过，可以跳转到下一步', stepOneData);

    if (currentStep.value < stepsList.value.length - 1) {
      currentStep.value++;
    }
  };

  // 根据文件扩展名判断文件类型的函数
  const getFileTypeByExtension = (filePath: string): string => {
    if (!filePath) return 'other';

    const extension = filePath.toLowerCase().split('.').pop();

    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'ppt':
      case 'pptx':
        return 'ppt';
      case 'xls':
      case 'xlsx':
        return 'excel';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return 'mp3';
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return 'zip';
      default:
        return 'other';
    }
  };

  // 构造附件列表的通用函数
  const buildAttachmentList = (bizType: string, materials: any) => {
    console.log('构造附件列表:', materials);
    const attachmentList: any[] = [];

    // 处理图片附件
    if (materials.images) {
      try {
        const images = typeof materials.images === 'string' ? JSON.parse(materials.images) : materials.images;
        if (Array.isArray(images)) {
          attachmentList.push(
            ...images.map((file: any) => ({
              bizType,
              fileName: file.fileName,
              filePath: file.filePath,
              fileSize: file.fileSize,
              fileType: getFileTypeByExtension(file.filePath),
            }))
          );
        }
      } catch (e) {
        console.warn('解析图片附件数据失败:', e);
      }
    }

    // 处理其他附件
    if (materials.attachments) {
      try {
        const attachments = typeof materials.attachments === 'string' ? JSON.parse(materials.attachments) : materials.attachments;
        if (Array.isArray(attachments)) {
          attachmentList.push(
            ...attachments.map((file: any) => ({
              bizType,
              fileName: file.fileName,
              filePath: file.filePath,
              fileSize: file.fileSize,
              fileType: getFileTypeByExtension(file.filePath),
            }))
          );
        }
      } catch (e) {
        console.warn('解析其他附件数据失败:', e);
      }
    }

    return attachmentList;
  };

  // 获取封面图片路径的函数
  const getCoverImage = (materials: any): string => {
    if (materials.images) {
      try {
        const images = typeof materials.images === 'string' ? JSON.parse(materials.images) : materials.images;
        if (Array.isArray(images) && images.length > 0) {
          return images[0].filePath || '';
        }
      } catch (e) {
        console.warn('解析图片数据失败:', e);
      }
    }
    return '';
  };

  // 提交表单
  const submitForm = async (status: number) => {
    // 验证第二步表单数据
    const isStep2Valid = await step2Ref.value?.validateForm();
    if (!isStep2Valid) {
      return;
    }

    submitting.value = true;

    try {
      const contactData = {
        contactName: stepTwoData.contact.contactName,
        contactPhone: stepTwoData.contact.contactPhone,
      };

      let apiData: PlatformUpdateParams;

      if (serviceType.value === 2) {
        // 发布资产处置
        const basicData = stepOneData.basicInfo;

        apiData = {
          id: isEditMode.value ? editId.value : undefined,
          entrustOrderId: stepOneData.entrustOrderId, // 关联委托单号
          entrustType: 1, // 委托类型(1-增值 2-自主) - 管理中心是增值
          serviceType: 2, // 服务类型(2-资产处置)
          status: status, // 状态(1-草稿 2-提交)
          entrustCompanyId: stepOneData.basicInfo.entrustCompanyId,
          assetName: basicData.assetName,
          assetNo: basicData.assetNo,
          assetType: basicData.assetType,
          quantity: basicData.quantity,
          unit: basicData.unit,
          quantityFlag: basicData.quantityFlag,
          serviceLife: Number(basicData.serviceLife),
          depreciationDegree: basicData.depreciationDegree,
          currentStatus: basicData.currentStatus,
          appraisalValue: Number(basicData.appraisalValue),
          disposalPrice: Number(basicData.disposalPrice),
          disposalStartTime: basicData.disposalStartTime,
          disposalEndTime: basicData.disposalEndTime,
          paymentMethod: basicData.paymentMethod,
          isTaxIncluded: basicData.isTaxIncluded,
          provinceCode: stepOneData.location.province,
          cityCode: stepOneData.location.city,
          districtCode: stepOneData.location.area,
          address: stepOneData.location.detailAddress,
          specialNotes: stepOneData.materials.specialNote,
          relationUser: contactData.contactName,
          relationPhone: contactData.contactPhone,
          coverImage: getCoverImage(stepOneData.materials),
          attachmentList: buildAttachmentList('zzcz', stepOneData.materials),
        };
      } else if (serviceType.value === 3) {
        // 发布采购信息
        apiData = {
          id: isEditMode.value ? editId.value : undefined,
          entrustOrderId: stepOneData.entrustOrderId, // 关联委托单号
          entrustType: 1, // 委托类型(1-增值 2-自主) - 管理中心是增值
          serviceType: 3, // 服务类型(3-采购信息)
          status: status, // 状态(1-草稿 2-提交)
          noticeName: stepOneData.entrustInfo.noticeName,
          provinceCode: stepOneData.location.province,
          cityCode: stepOneData.location.city,
          districtCode: stepOneData.location.area,
          address: stepOneData.location.detailAddress,
          relationUser: contactData.contactName,
          relationPhone: contactData.contactPhone,
          attachmentList: buildAttachmentList('zzcg', stepOneData.materials),
        };
      } else {
        throw new Error('不支持的服务类型');
      }

      console.log('提交数据:', apiData);
      const result = await platformUpdate(apiData);

      if (result) {
        message.success(status === 1 ? '保存草稿成功' : '发布成功');
        // 跳转到列表页面
        router.push('/manageCenter/appreciationPublish/list');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    } finally {
      submitting.value = false;
    }
  };
</script>

<style lang="less" scoped>
  // 主容器样式
  .appreciation-publish {
    padding: 24px;
    background-color: #fff;
    height: calc(100vh - 142px); // 减去头部和底部的高度
    overflow-y: auto;
    margin: 19px 30px 30px 30px;
    border-radius: 10px;
  }

  // 步骤条包装器
  .steps-wrapper {
    margin-bottom: 32px;
  }

  // 步骤内容
  .step-content {
    margin: 0 auto; // 居中显示
  }

  // 步骤面板
  .step-panel {
    background-color: #fff;
    border-radius: 8px;
  }

  // 操作按钮区域
  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px; // 按钮之间的间距
    margin-top: 48px; // 与表单内容的间距
    padding-top: 24px;

    .next-btn,
    .submit-btn,
    .draft-btn {
      width: 434px; // 按钮最小宽度
      height: 48px; // 按钮高度
      font-size: 16px; // 字体大小
      border-radius: 6px; // 圆角
    }

    .next-btn,
    .submit-btn {
      background-color: #004c66; // 主色调
      border-color: #004c66;

      &:hover:not(:disabled) {
        background: rgba(0, 76, 102, 0.9);
      }
    }

    .draft-btn {
      background-color: #fff;
      color: #666;
      border-color: #d9d9d9;

      &:hover {
        color: #004c66;
        border-color: #004c66;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .step-content {
      max-width: 100%;
      padding: 0 16px;
    }

    .step-panel {
      padding: 24px 16px;
    }
  }

  @media (max-width: 768px) {
    .appreciation-publish {
      padding: 16px;
    }

    .steps-wrapper {
      padding: 0 8px;
      margin-bottom: 24px;
    }

    .step-content {
      padding: 0 8px;
    }

    .step-panel {
      padding: 16px;
      border-radius: 4px;
    }

    .step-actions {
      flex-direction: column; // 小屏幕下垂直排列
      gap: 12px;
      margin-top: 32px;

      .next-btn,
      .submit-btn,
      .draft-btn {
        width: 100%; // 小屏幕下占满宽度
      }
    }
  }
</style>
