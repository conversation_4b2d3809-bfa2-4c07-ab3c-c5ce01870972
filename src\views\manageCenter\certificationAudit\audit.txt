企业认证审核和查看详情页面布局和显示字段如下：
个人认证和企业认证获取到的详情数据结构一致
企业认证中的详情数据中有两个对象：
hgyAttachmentList:[
  {},
  {}
]该数组中是身份证正反面照片
hgyEnterpriseAuth{
  cartId：'',//法人证件号
  cartType: number//法人证件类型(1.身份证 2.其他)
  legalName：'',//法人真实姓名
  relationUser：'',//联系人
  relationPhone：'',//联系电话
  enterpriseName：'',//企业名称
  creditCode：'',//企业统一信用代码
  companyLogo：'',//营业执照照片
  tenantId：'',//租户id
  review：'',//审核状态
  notes：'',//审核说明，仅在审核完成后显示
  reviewUser：'',//审核人
}该对象中是详细信息

个人认证中的详情数据中有两个对象：
hgyAttachmentList:[
  {},
  {}
]该数组中是身份证正反面照片
hgyPersonalAuth{
  cartId：'',//证件号
  cartType: number//证件类型(1.身份证 2.其他)
  name：'',//真实姓名
  phone：'',//手机号
  tenantId：'',//租户id
  review：'',//审核状态
  notes：'',//审核说明，仅在审核完成后显示
  reviewUser：'',//审核人
}该对象中是详细信息


企业认证审核和查看详情弹窗资料区域显示分为两个板块：企业资料和法人信息
企业资料需要显示企业名称，统一社会信用代码，业务联系人，联系电话，营业执照照片
法人信息需要显示法人真实姓名，法人证件类型，法人证件号，身份证正反面照片

个人认证审核和查看详情弹窗资料区域显示只有一个板块：个人资料
个人资料需要显示真实姓名，手机号，证件类型，证件号，身份证正反面照片
