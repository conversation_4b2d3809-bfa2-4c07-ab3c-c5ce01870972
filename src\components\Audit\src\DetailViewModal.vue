<template>
  <CustomModal
    v-model:open="visible"
    :title="modalTitle"
    :width="1200"
    :show-footer="false"
    :destroy-on-close="true"
    @close="handleClose"
    @cancel="handleClose"
  >
    <!-- 增值委托详情 -->
    <CommonAppreciationAudit v-if="entrustType === 1" :record="record" :service-type="serviceType" :view-only="true" @close="handleClose" />

    <!-- 自主委托详情 -->
    <CommonSelfAudit v-else-if="entrustType === 2" :record="record" :service-type="serviceType" :view-only="true" @close="handleClose" />
  </CustomModal>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { CustomModal } from '/@/components/Modal';
  import CommonAppreciationAudit from './CommonAppreciationAudit.vue';
  import CommonSelfAudit from './CommonSelfAudit.vue';
  import type { AuditRecord } from '../types';

  interface Props {
    open: boolean;
    record: AuditRecord | null;
    entrustType: number; // 1: 增值委托, 2: 自主委托
    serviceType: number; // 1: 竞价委托, 2: 资产处置, 3: 采购信息
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'close'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const visible = ref(false);

  // 计算弹窗标题
  const modalTitle = computed(() => {
    const entrustTypeText = props.entrustType === 1 ? '增值委托' : '自主委托';
    let serviceTypeText = '';

    switch (props.serviceType) {
      case 1:
        serviceTypeText = '竞价委托';
        break;
      case 2:
        serviceTypeText = '资产处置';
        break;
      case 3:
        serviceTypeText = '采购信息';
        break;
      default:
        serviceTypeText = '详情';
    }

    return `${entrustTypeText} - ${serviceTypeText}详情`;
  });

  // 监听外部传入的open状态
  watch(
    () => props.open,
    (newVal) => {
      visible.value = newVal;
    },
    { immediate: true }
  );

  // 监听内部visible状态变化
  watch(visible, (newVal) => {
    emit('update:open', newVal);
  });

  // 关闭弹窗
  function handleClose() {
    visible.value = false;
    emit('close');
  }
</script>

<style lang="less" scoped>
  :deep(.custom-modal-content) {
    padding: 24px 49px;
    max-height: 80vh;
    overflow-y: auto;
    // 审核组件根容器样式调整
    .appreciation-audit,
    .self-audit {
      .audit-container {
        padding: 0;
      }

      .loading-container {
        padding: 40px 0;
        text-align: center;
      }
    }
  }
</style>
