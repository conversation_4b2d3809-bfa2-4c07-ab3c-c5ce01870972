<template>
  <div class="p-4">
    <BasicTable @register="registerTable" @navigation-change="handleNavigationChange" @export="handleExport">
      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownActions(record)" />
      </template>

      <!-- 审核状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>

      <!-- 时间格式化 -->
      <template #createTime="{ text }">
        <span v-if="text">{{ formatToDateTime(text) }}</span>
        <span v-else>-</span>
      </template>

      <template #updateTime="{ text }">
        <span v-if="text">{{ formatToDateTime(text) }}</span>
        <span v-else>-</span>
      </template>
    </BasicTable>

    <!-- 详情查看弹窗 -->
    <DetailViewModal v-model:open="detailVisible" :record="currentRecord" :entrust-type="1" :service-type="3" @close="handleDetailClose" />
  </div>
</template>

<script lang="ts" setup name="ProcurementInfo">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { list, deleteOne, getExportUrl } from '/@/api/orderManage/procurementInfo';
  import { DetailViewModal } from '/@/components/Audit';
  import type { AuditRecord } from '/@/components/Audit/types';
  import { useMethods } from '/@/hooks/system/useMethods';

  const { createMessage } = useMessage();
  const { handleExportXls } = useMethods();
  const router = useRouter();

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '服务单ID',
      dataIndex: 'entrustOrderId',
      width: 160,
    },
    {
      title: '公告名称',
      dataIndex: 'noticeName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '省份',
      dataIndex: 'province',
      width: 100,
    },
    {
      title: '城市',
      dataIndex: 'city',
      width: 100,
    },
    {
      title: '区县',
      dataIndex: 'district',
      width: 100,
    },
    {
      title: '详细地址',
      dataIndex: 'address',
      width: 200,
      ellipsis: true,
    },
    {
      title: '特殊说明',
      dataIndex: 'specialNotes',
      width: 150,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      slots: { customRender: 'createTime' },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 150,
      slots: { customRender: 'updateTime' },
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      width: 100,
    },
    {
      title: '更新人',
      dataIndex: 'updateBy',
      width: 100,
    },
  ];

  // 导航栏配置
  const navigationItems = [
    { key: 'all', label: '全部采购', icon: '' },
    { key: 'draft', label: '草稿', icon: '' },
    { key: 'pending', label: '待审核', icon: '' },
    { key: 'approved', label: '已通过', icon: '' },
    { key: 'rejected', label: '未通过', icon: '' },
  ];

  const activeNavigationKey = ref<string | number>('all');
  const currentNavParams = ref<any>({});
  const detailVisible = ref(false);
  const currentRecord = ref<AuditRecord | null>(null);

  // 处理导航切换
  function handleNavigationChange(key: string | number) {
    activeNavigationKey.value = key;

    let searchParams = {};
    switch (key) {
      case 'draft':
        searchParams = { status: 1 };
        break;
      case 'pending':
        searchParams = { status: 2 };
        break;
      case 'approved':
        searchParams = { status: 3 };
        break;
      case 'rejected':
        searchParams = { status: 4 };
        break;
      default:
        searchParams = {};
    }

    currentNavParams.value = searchParams;
    reload();
  }

  // 自定义API调用函数
  async function customQueryPageAll(params: any) {
    const defaultParams = {
      column: 'createTime',
      order: 'desc', // 采购服务
    };

    const mergedParams = {
      ...params,
      ...defaultParams,
      ...currentNavParams.value,
    };

    return list(mergedParams);
  }

  // 处理导出
  async function handleExport() {
    try {
      let searchParams = {};
      try {
        searchParams = await getForm().validate();
      } catch (error) {
        console.warn('表单验证失败，使用空参数导出:', error);
      }

      const exportParams = {
        ...currentNavParams.value,
        ...searchParams,
      };

      await handleExportXls('采购信息列表', getExportUrl, exportParams);
      createMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      createMessage.error('导出失败');
    }
  }

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    api: customQueryPageAll,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    // 导航栏配置
    showNavigation: true,
    navigationItems,
    activeNavigationKey: activeNavigationKey.value,
    showExportButton: true,
    inset: true,
    maxHeight: 500,
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: [
        {
          field: 'entrustOrderId',
          label: '服务单ID',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'noticeName',
          label: '公告名称',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'province',
          label: '省份',
          component: 'Input',
          colProps: { span: 6 },
        },
        {
          field: 'city',
          label: '城市',
          component: 'Input',
          colProps: { span: 6 },
        },
      ],
    },
  });

  // 操作按钮配置
  function getTableAction(record: any): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '查看详情',
        onClick: handleViewDetail.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确认删除该采购信息吗？',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }

  // 下拉操作按钮配置
  function getDropDownActions(_record: any): ActionItem[] {
    return [];
  }

  // 事件处理函数
  function handleEdit(record: any) {
    createMessage.info('编辑功能开发中...');
    console.log('编辑记录:', record);
  }

  function handleViewDetail(record: any) {
    // 转换数据格式为 AuditRecord
    const auditRecord: AuditRecord = {
      id: record.id,
      entrustType: 1,
      serviceType: 3, // 采购服务
      status: record.status || 1,
      projectName: record.noticeName || '-',
      relationUser: record.createBy || '-',
      relationPhone: '-',
      applicantUser: record.createBy || '-',
      auditUser: '-',
      submitTime: record.createTime || '-',
      auditTime: record.updateTime || '-',
    };

    currentRecord.value = auditRecord;
    detailVisible.value = true;
  }

  // 关闭详情弹窗
  function handleDetailClose() {
    detailVisible.value = false;
    currentRecord.value = null;
  }

  async function handleDelete(record: any) {
    try {
      await deleteOne({ id: record.id });
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 状态处理函数
  function getStatusText(status: number) {
    const statusMap: Record<number, string> = {
      1: '草稿',
      2: '待审核',
      3: '审核通过',
      4: '审核拒绝',
      5: '已发布',
      6: '已完成',
    };
    return statusMap[status] || '未知';
  }

  function getStatusColor(status: number) {
    const colorMap: Record<number, string> = {
      1: 'default',
      2: 'processing',
      3: 'success',
      4: 'error',
      5: 'cyan',
      6: 'green',
    };
    return colorMap[status] || 'default';
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
    :deep(.ant-form) {
      padding: 0;
    }
  }
</style>
