import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/hgy/entrustService/hgyProcurement/list',
  save = '/hgy/entrustService/hgyProcurement/add',
  edit = '/hgy/entrustService/hgyProcurement/edit',
  deleteOne = '/hgy/entrustService/hgyProcurement/customProcurementDelete',
  deleteBatch = '/hgy/entrustService/hgyProcurement/deleteBatch',
  importExcel = '/hgy/entrustService/hgyProcurement/importExcel',
  exportXls = '/hgy/entrustService/hgyProcurement/customProcurementExportXls',
  queryById = '/hgy/entrustService/hgyProcurement/queryProcurementById',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 根据ID查询详情
 */
export const queryById = (params) => defHttp.get({ url: Api.queryById, params });

/**
 * 删除单个
 */
export const deleteOne = (params) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true });
};

/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

// 采购信息记录类型定义
export interface ProcurementInfoRecord {
  id: string;
  entrustOrderId: string;
  noticeName: string;
  province: string;
  city: string;
  district: string;
  address: string;
  specialNotes: string;
  status?: number;
  entrustType?: number;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
}
