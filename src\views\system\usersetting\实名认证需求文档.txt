个人认证接口：/hgy/personalCenter/hgyPersonalAuth/addOrUpdatePersonalAuth，post请求，
上传数据结构：{
"cartId": "430405200001012000", // 证件号
"name": "韩富kun2号", // 真实姓名
"review": 1, // 审核状态
"phone": "18910749865", // 手机号
"cartType": 1, // 证件类型(1.身份证 2.其他)
"attachmentList": [ // 证件照片信息
{
"bizType": "iamge",
"fileName": "测试1",
"filePath": "http://localhost:80/测试07121357.jpg",
"fileSize": "8192",
"fileType": "image"
},
{
"bizType": "iamge",
"fileName": "测试2",
"filePath": "http://localhost:80/测试07121358.jpg",
"fileSize": "8192",
"fileType": "xls"
}
]
}
企业认证接口：/hgy/personalCenter/hgyEnterpriseAuth/addOrUpdateEnterpriseAuth，post请求
上传数据结构：{
"review": 1, // 审核状态
"legalName": "测试法人姓名071803JJ", // 法人姓名
"enterpriseName": "测试企业05", // 企业名称
"creditCode": "HN9005", // 信用代码
"relationUser": "测试用户071803", // 联系人
"relationPhone": "18238369056", // 联系电话
"cartType": 1, // 法人证件类型(1.身份证 2.其他)
"cartId": "******************", // 法人证件号
"attachmentList": [ // 身份证正反面照片信息
{
"bizType": "iamge",
"fileName": "测试1",
"filePath": "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1e7ae571abd0040e0ec040dd07ca7ffa(1)_1752487587319.jpg",
"fileSize": "8192",
"fileType": "image"
},
{
"bizType": "iamge",
"fileName": "测试2",
"filePath": "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1e7ae571abd0040e0ec040dd07ca7ffa(1)_1752487587319.jpg",
"fileSize": "8192",
"fileType": "xls"
}
],
"description": "测试回收商的企业05是一家大型多手设备出售的企业，第二次更新", // 企业简介
"companyLogo": "https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1e7ae571abd0040e0ec040dd07ca7ffa(1)_1752487587319.jpg" // 营业执照照片
}
个人认证信息获取接口：/hgy/personalCenter/hgyPersonalAuth/getPersonalAuthByUserId，get请求，需携带参数tenantId
企业认证信息获取接口：/hgy/personalCenter/hgyEnterpriseAuth/getEnterpriseAuthByUserId，get请求，需携带参数tenantId
