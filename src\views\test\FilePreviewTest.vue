<template>
  <div class="file-preview-test">
    <a-card title="文件预览测试">
      <a-space direction="vertical" style="width: 100%">
        <div>
          <a-input 
            v-model:value="testFileUrl" 
            placeholder="请输入文件URL" 
            style="width: 400px"
          />
          <a-input 
            v-model:value="testFileName" 
            placeholder="请输入文件名（包含扩展名）" 
            style="width: 200px; margin-left: 10px"
          />
          <a-button type="primary" @click="previewFile" style="margin-left: 10px">
            预览文件
          </a-button>
        </div>
        
        <div>
          <h3>测试文件示例：</h3>
          <a-space wrap>
            <a-button @click="testPdf">测试PDF</a-button>
            <a-button @click="testWord">测试Word</a-button>
            <a-button @click="testExcel">测试Excel</a-button>
            <a-button @click="testImage">测试图片</a-button>
          </a-space>
        </div>
        
        <div v-if="currentFile">
          <h3>当前测试文件：</h3>
          <p>文件名: {{ currentFile.fileName }}</p>
          <p>文件路径: {{ currentFile.filePath }}</p>
          <p>是否支持预览: {{ isSupportPreview(currentFile.fileName) ? '是' : '否' }}</p>
        </div>
      </a-space>
    </a-card>

    <!-- 文件预览组件 -->
    <FilePreview v-model:open="previewVisible" :file-info="currentFile" @close="closePreview" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import FilePreview from '/@/components/FilePreview/src/FilePreview.vue';
  import { useFilePreview } from '/@/components/FilePreview/src/useFilePreview';

  const testFileUrl = ref('');
  const testFileName = ref('');

  // 文件预览相关
  const { visible: previewVisible, currentFile, openPreview, closePreview, isSupportPreview } = useFilePreview();

  function previewFile() {
    if (!testFileUrl.value || !testFileName.value) {
      message.warning('请输入文件URL和文件名');
      return;
    }

    openPreview({
      fileName: testFileName.value,
      filePath: testFileUrl.value,
      fileType: getFileExtension(testFileName.value),
    });
  }

  function getFileExtension(fileName: string): string {
    return fileName.toLowerCase().split('.').pop() || '';
  }

  // 测试不同类型的文件
  function testPdf() {
    testFileUrl.value = 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf';
    testFileName.value = 'test.pdf';
    previewFile();
  }

  function testWord() {
    testFileUrl.value = 'https://file-examples.com/storage/fe68c8c7c66d7b62c8e8e8e/2017/10/file_example_DOC_100kB.doc';
    testFileName.value = 'test.doc';
    previewFile();
  }

  function testExcel() {
    testFileUrl.value = 'https://file-examples.com/storage/fe68c8c7c66d7b62c8e8e8e/2017/10/file_example_XLS_10.xls';
    testFileName.value = 'test.xls';
    previewFile();
  }

  function testImage() {
    testFileUrl.value = 'https://picsum.photos/800/600';
    testFileName.value = 'test.jpg';
    previewFile();
  }
</script>

<style lang="less" scoped>
  .file-preview-test {
    padding: 20px;
  }
</style>
