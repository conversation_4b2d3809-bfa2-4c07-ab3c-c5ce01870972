<template>
  <CustomModal
    v-model:open="visible"
    title="认证审核"
    width="800px"
    :show-footer="true"
    :show-cancel-button="true"
    :show-confirm-button="true"
    cancel-text="取消"
    confirm-text="确认审核"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div v-if="record" class="audit-content" v-loading="loading">
      <!-- 企业认证资料 -->
      <template v-if="record.authType === '企业认证'">
        <!-- 企业资料 -->
        <div class="info-section">
          <span class="section-title">企业资料</span>
          <a-descriptions :column="2" bordered size="small" v-if="detailData?.hgyEnterpriseAuth">
            <a-descriptions-item label="企业名称">{{ detailData.hgyEnterpriseAuth.enterpriseName || '-' }}</a-descriptions-item>
            <a-descriptions-item label="统一社会信用代码">{{ detailData.hgyEnterpriseAuth.creditCode || '-' }}</a-descriptions-item>
            <a-descriptions-item label="业务联系人">{{ detailData.hgyEnterpriseAuth.relationUser || '-' }}</a-descriptions-item>
            <a-descriptions-item label="联系电话">{{ detailData.hgyEnterpriseAuth.relationPhone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="营业执照照片" :span="2">
              <div v-if="detailData.hgyEnterpriseAuth.companyLogo" class="image-preview">
                <Image :src="detailData.hgyEnterpriseAuth.companyLogo" :width="200" :preview="true" />
              </div>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 法人信息 -->
        <div class="info-section">
          <span class="section-title">法人信息</span>
          <a-descriptions :column="2" bordered size="small" v-if="detailData?.hgyEnterpriseAuth">
            <a-descriptions-item label="法人真实姓名">{{ detailData.hgyEnterpriseAuth.legalName || '-' }}</a-descriptions-item>
            <a-descriptions-item label="法人证件类型">{{ getCartTypeText(detailData.hgyEnterpriseAuth.cartType) }}</a-descriptions-item>
            <a-descriptions-item label="法人证件号" :span="2">{{ detailData.hgyEnterpriseAuth.cartId || '-' }}</a-descriptions-item>
            <a-descriptions-item label="身份证正反面照片" :span="2">
              <div v-if="detailData.hgyAttachmentList && detailData.hgyAttachmentList.length > 0" class="image-preview">
                <Image v-for="(item, index) in detailData.hgyAttachmentList" :key="index" :src="item.filePath" :preview="true" />
              </div>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </template>

      <!-- 个人认证资料 -->
      <template v-else-if="record.authType === '个人认证'">
        <!-- 个人资料 -->
        <div class="info-section">
          <span class="section-title">个人资料</span>
          <a-descriptions :column="2" bordered size="small" v-if="detailData?.hgyPersonalAuth">
            <a-descriptions-item label="真实姓名">{{ detailData.hgyPersonalAuth.name || '-' }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ detailData.hgyPersonalAuth.phone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="证件类型">{{ getCartTypeText(detailData.hgyPersonalAuth.cartType) }}</a-descriptions-item>
            <a-descriptions-item label="证件号">{{ detailData.hgyPersonalAuth.cartId || '-' }}</a-descriptions-item>
            <a-descriptions-item label="身份证正反面照片" :span="2">
              <div v-if="detailData.hgyAttachmentList && detailData.hgyAttachmentList.length > 0" class="image-preview">
                <Image
                  v-for="(item, index) in detailData.hgyAttachmentList"
                  :key="index"
                  :src="item.filePath"
                  :width="150"
                  :preview="true"
                  style="margin-right: 10px"
                />
              </div>
              <span v-else>-</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </template>

      <!-- 审核操作 -->
      <div class="audit-section">
        <span class="section-title">审核操作</span>
        <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
          <a-form-item label="审核结果" name="review" required>
            <a-radio-group v-model:value="formData.review">
              <a-radio :value="2">通过</a-radio>
              <a-radio :value="3">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="审核意见" name="notes">
            <a-textarea v-model:value="formData.notes" placeholder="请输入审核意见（选填）" :rows="4" :maxlength="500" show-count />
          </a-form-item>
        </a-form>
      </div>
    </div>
  </CustomModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, computed } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    getPersonalAuthDetail,
    getEnterpriseAuthDetail,
    reviewPersonalAuth,
    reviewEnterpriseAuth,
    CertificationAuditRecord,
  } from '/@/api/manageCenter/certificationAudit';
  import CustomModal from '/@/components/Modal/src/CustomModal.vue';
  import { Image } from 'ant-design-vue';

  interface Props {
    open: boolean;
    record: CertificationAuditRecord | null;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'success'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const { createMessage } = useMessage();

  const visible = computed({
    get: () => props.open,
    set: (value) => emit('update:open', value),
  });

  const confirmLoading = ref(false);
  const loading = ref(false);
  const formRef = ref();
  const detailData = ref<any>(null);

  // 表单数据
  const formData = reactive({
    review: 2, // 默认通过
    notes: '',
  });

  // 表单验证规则
  const rules = {
    review: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  };

  // 监听弹窗打开，重置表单并获取详情
  watch(
    () => props.open,
    async (newVal) => {
      if (newVal) {
        formData.review = 2;
        formData.notes = '';
        if (props.record) {
          await fetchDetailData();
        }
      }
    }
  );

  // 获取详细信息
  async function fetchDetailData() {
    if (!props.record) return;

    try {
      loading.value = true;

      // 根据认证类型调用不同的详情接口
      let result: any;
      if (props.record.authType === '个人认证') {
        result = await getPersonalAuthDetail(props.record.id);
      } else if (props.record.authType === '企业认证') {
        result = await getEnterpriseAuthDetail(props.record.id);
      } else {
        createMessage.error('未知的认证类型');
        return;
      }

      detailData.value = result;
    } catch (error) {
      console.error('获取详情失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 获取证件类型文本
  function getCartTypeText(type: number): string {
    switch (type) {
      case 1:
        return '身份证';
      case 2:
        return '其他';
      default:
        return '-';
    }
  }

  // 提交审核
  async function handleSubmit() {
    try {
      await formRef.value?.validate();

      if (!props.record) {
        createMessage.error('记录信息不存在');
        return;
      }

      confirmLoading.value = true;

      // 根据认证类型调用不同的审核接口
      const reviewParams = {
        id: props.record.id,
        review: formData.review,
        notes: formData.notes || undefined,
      };

      if (props.record.authType === '个人认证') {
        await reviewPersonalAuth(reviewParams);
      } else if (props.record.authType === '企业认证') {
        await reviewEnterpriseAuth(reviewParams);
      } else {
        createMessage.error('未知的认证类型');
        return;
      }
      emit('success');
    } catch (error) {
      console.error('审核失败:', error);
    } finally {
      confirmLoading.value = false;
    }
  }

  // 取消操作
  function handleCancel() {
    visible.value = false;
    detailData.value = null;
  }
</script>

<style lang="less" scoped>
  .audit-content {
    padding-top: 20px;

    .info-section,
    .audit-section {
      margin-bottom: 12px;

      .section-title {
        display: block;
        margin-bottom: 10px;
        font-size: 16px;
        font-family: 'PingFang Bold';
        color: #262626;
        border-left: 4px solid #004c66;
        padding-left: 12px;
      }
    }

    .audit-section {
      border-top: 1px solid #f0f0f0;
      padding-top: 24px;
    }

    .image-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      :deep(.ant-image) {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        overflow: hidden;
      }
    }
  }
</style>
