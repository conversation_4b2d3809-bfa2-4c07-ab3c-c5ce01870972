<template>
  <div class="verification-container">
    <!-- 顶部导航切换 -->
    <div class="verification-nav">
      <div
        class="nav-tab enterprise-nav-tab"
        :class="{ active: activeTab === 2 }"
        @click="switchTab(2)"
      >
        企业认证
      </div>
      <div
        class="nav-tab personal-nav-tab"
        :class="{ active: activeTab === 1 }"
        @click="switchTab(1)"
      >
        个人认证
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="verification-content">
      <!-- 企业认证组件 -->
      <EnterpriseVerification
        v-if="activeTab === 2"
        ref="enterpriseVerificationRef"
        :areaOptions="areaOptions"
        :recycleOptions="recycleOptions"
        @save="handleEnterpriseSave"
        @submit="handleEnterpriseSubmit"
      />

      <!-- 个人认证组件 -->
      <PersonalVerification
        v-if="activeTab === 1"
        ref="personalVerificationRef"
        :areaOptions="areaOptions"
        :recycleOptions="recycleOptions"
        @save="handlePersonalSave"
        @submit="handlePersonalSubmit"
      />
    </div>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <button class="save-btn" @click="handleSave">保存</button>
      <button class="submit-btn" @click="handleSubmit">提交审核</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { userApi } from "@/utils/api";
import { initAreaData, type AreaOption } from "./verification/areaUtils";
import PersonalVerification from "./verification/PersonalVerification.vue";
import EnterpriseVerification from "./verification/EnterpriseVerification.vue";

// 当前激活的标签页
const activeTab = ref<2 | 1>(2);

// 地区数据
const areaOptions = ref<AreaOption[]>([]);

// 回收种类选项
const recycleOptions = ref([
  { value: '1', label: '物资设备' },
  { value: '2', label: '机动车' },
  { value: '3', label: '房产' },
  { value: '4', label: '土地' },
  { value: '5', label: '其他' }
]);

// 子组件引用
const personalVerificationRef = ref<InstanceType<typeof PersonalVerification>>();
const enterpriseVerificationRef = ref<InstanceType<typeof EnterpriseVerification>>();

/**
 * 切换标签页
 * @param tab 标签页类型
 */
const switchTab = (tab: 2 | 1) => {
  activeTab.value = tab;
};

/**
 * 处理个人认证保存
 * @param data 个人认证数据
 */
const handlePersonalSave = async (data: any) => {
  try {
    const result = await userApi.uploadCertification(data);
    if (result.code === 1) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(result.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

/**
 * 处理个人认证提交
 * @param data 个人认证数据
 */
const handlePersonalSubmit = async (data: any) => {
  try {
    const result = await userApi.uploadCertification(data);
    if (result.code === 200) {
      ElMessage.success("提交审核成功");
    } else {
      ElMessage.error(result.msg || "提交失败");
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

/**
 * 处理企业认证保存
 * @param data 企业认证数据
 */
const handleEnterpriseSave = async (data: any) => {
  try {
    const result = await userApi.uploadCertification(data);
    if (result.code === 1) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(result.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

/**
 * 处理企业认证提交
 * @param data 企业认证数据
 */
const handleEnterpriseSubmit = async (data: any) => {
  try {
    const result = await userApi.uploadCertification(data);
    if (result.code === 200) {
      ElMessage.success("提交审核成功");
    } else {
      ElMessage.error(result.msg || "提交失败");
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  }
};

/**
 * 处理保存操作
 */
const handleSave = () => {
  if (activeTab.value === 1) {
    personalVerificationRef.value?.handleSave();
  } else {
    enterpriseVerificationRef.value?.handleSave();
  }
};

/**
 * 处理提交审核操作
 */
const handleSubmit = () => {
  if (activeTab.value === 1) {
    personalVerificationRef.value?.handleSubmit();
  } else {
    enterpriseVerificationRef.value?.handleSubmit();
  }
};

// 组件挂载时初始化地区数据
onMounted(() => {
  areaOptions.value = initAreaData();
});
</script>

<style scoped lang="scss">
.verification-container {
  .verification-nav {
    display: flex;
    height: 55px; // 导航区域高度
    width: 100%; // 宽度铺满页面
    border-radius: 10px;

    .nav-tab {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px; // 文字大小
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #eee; // 未选中状态背景色
      color: #999; // 未选中状态文字颜色

      &.active {
        background-color: #fff; // 选中状态背景色
        color: #004c66; // 选中状态字体颜色
      }

      &:hover:not(.active) {
        background-color: #e0e0e0; // 悬停效果
      }
    }

    .enterprise-nav-tab {
      border-top-left-radius: 10px;
    }

    .personal-nav-tab {
      border-top-right-radius: 10px;
    }
  }

  .verification-content {
    padding: 20px;
    min-height: 670px;
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    padding: 0 20px 20px 20px;

    .save-btn,
    .submit-btn {
      padding: 9px 24px;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;
    }

    .save-btn {
      background-color: rgba(0, 76, 102, 0.2); // 保存按钮背景色
      color: #004c66; // 保存按钮字体颜色

      &:hover {
        background-color: rgba(0, 76, 102, 0.3);
      }
    }

    .submit-btn {
      background-color: #004c66; // 提交审核按钮背景色
      color: #fff; // 提交审核按钮字体颜色

      &:hover {
        background-color: #003a4d;
      }
    }
  }
}
</style>
