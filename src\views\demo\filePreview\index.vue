<template>
  <div class="file-preview-demo">
    <a-card title="文件预览组件演示">
      <div class="demo-section">
        <h3>支持的文件类型</h3>
        <div class="file-list">
          <div v-for="file in demoFiles" :key="file.fileName" class="file-item">
            <div class="file-info">
              <a-tag :color="getFileTypeColor(file.type)">{{ file.type.toUpperCase() }}</a-tag>
              <span class="file-name">{{ file.fileName }}</span>
            </div>
            <div class="file-actions">
              <a-button 
                type="primary" 
                size="small" 
                @click="handlePreview(file)"
                :disabled="!isSupportPreview(file.fileName)"
              >
                {{ isSupportPreview(file.fileName) ? '预览' : '不支持预览' }}
              </a-button>
              <a-button size="small" @click="handleDownload(file)">下载</a-button>
            </div>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>自定义文件预览</h3>
        <a-form layout="inline">
          <a-form-item label="文件名">
            <a-input v-model:value="customFile.fileName" placeholder="请输入文件名" />
          </a-form-item>
          <a-form-item label="文件路径">
            <a-input v-model:value="customFile.filePath" placeholder="请输入文件URL" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleCustomPreview">预览自定义文件</a-button>
          </a-form-item>
        </a-form>
      </div>
    </a-card>

    <!-- 文件预览组件 -->
    <FilePreview
      v-model:open="previewVisible"
      :file-info="currentFile"
      @close="closePreview"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import FilePreview from '/@/components/FilePreview';
  import { useFilePreview } from '/@/components/FilePreview/src/useFilePreview';

  // 使用文件预览组合函数
  const { 
    visible: previewVisible, 
    currentFile, 
    openPreview, 
    closePreview, 
    isSupportPreview,
    getFileType 
  } = useFilePreview();

  // 演示文件列表
  const demoFiles = ref([
    {
      fileName: 'sample.pdf',
      filePath: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
      type: 'pdf'
    },
    {
      fileName: 'image.jpg',
      filePath: 'https://picsum.photos/800/600',
      type: 'image'
    },
    {
      fileName: 'document.docx',
      filePath: 'https://file-examples.com/storage/fe68c8a7c4c71c2c8e5e8e8/2017/10/file_example_DOCX_10.docx',
      type: 'word'
    },
    {
      fileName: 'spreadsheet.xlsx',
      filePath: 'https://file-examples.com/storage/fe68c8a7c4c71c2c8e5e8e8/2017/10/file_example_XLSX_10.xlsx',
      type: 'excel'
    },
    {
      fileName: 'presentation.pptx',
      filePath: 'https://file-examples.com/storage/fe68c8a7c4c71c2c8e5e8e8/2017/10/file_example_PPTX_1MB.pptx',
      type: 'powerpoint'
    },
    {
      fileName: 'video.mp4',
      filePath: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      type: 'video'
    },
    {
      fileName: 'audio.mp3',
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.mp3',
      type: 'audio'
    },
    {
      fileName: 'text.txt',
      filePath: 'https://www.w3.org/TR/PNG/iso_8859-1.txt',
      type: 'text'
    },
    {
      fileName: 'archive.zip',
      filePath: 'https://sample-files.com/samples/Archives/zip/10mb/zip.zip',
      type: 'other'
    }
  ]);

  // 自定义文件
  const customFile = reactive({
    fileName: '',
    filePath: ''
  });

  // 预览文件
  function handlePreview(file: any) {
    openPreview({
      fileName: file.fileName,
      filePath: file.filePath,
      fileType: file.type
    });
  }

  // 下载文件
  function handleDownload(file: any) {
    const link = document.createElement('a');
    link.href = file.filePath;
    link.download = file.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success(`开始下载 ${file.fileName}`);
  }

  // 预览自定义文件
  function handleCustomPreview() {
    if (!customFile.fileName || !customFile.filePath) {
      message.warning('请输入文件名和文件路径');
      return;
    }

    openPreview({
      fileName: customFile.fileName,
      filePath: customFile.filePath,
      fileType: getFileType(customFile.fileName)
    });
  }

  // 获取文件类型颜色
  function getFileTypeColor(type: string): string {
    const colorMap: Record<string, string> = {
      image: 'green',
      pdf: 'red',
      word: 'blue',
      excel: 'orange',
      powerpoint: 'purple',
      video: 'cyan',
      audio: 'magenta',
      text: 'gray',
      other: 'default'
    };
    return colorMap[type] || 'default';
  }
</script>

<style lang="less" scoped>
  .file-preview-demo {
    padding: 20px;

    .demo-section {
      margin-bottom: 32px;

      h3 {
        margin-bottom: 16px;
        color: #333;
        font-weight: 600;
      }

      .file-list {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e9ecef;

          .file-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .file-name {
              font-weight: 500;
              color: #333;
            }
          }

          .file-actions {
            display: flex;
            gap: 8px;
          }
        }
      }
    }
  }
</style>
