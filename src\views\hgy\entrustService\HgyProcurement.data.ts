import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '服务单ID',
    align:"center",
    dataIndex: 'entrustOrderId'
   },
   {
    title: '公告名称',
    align:"center",
    dataIndex: 'noticeName'
   },
   {
    title: '省份',
    align:"center",
    dataIndex: 'province'
   },
   {
    title: '城市',
    align:"center",
    dataIndex: 'city'
   },
   {
    title: '区县',
    align:"center",
    dataIndex: 'district'
   },
   {
    title: '详细地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '特殊说明',
    align:"center",
    dataIndex: 'specialNotes'
   },
   {
    title: '创建时间',
    align:"center",
    dataIndex: 'createTime'
   },
   {
    title: '更新时间',
    align:"center",
    dataIndex: 'updateTime'
   },
   {
    title: '创建人',
    align:"center",
    dataIndex: 'createBy'
   },
   {
    title: '更新人',
    align:"center",
    dataIndex: 'updateBy'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '公告名称',
    field: 'noticeName',
    component: 'Input',
  },
  {
    label: '省份',
    field: 'province',
    component: 'Input',
  },
  {
    label: '城市',
    field: 'city',
    component: 'Input',
  },
  {
    label: '区县',
    field: 'district',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  entrustOrderId: {title: '服务单ID',order: 0,view: 'text', type: 'string',},
  noticeName: {title: '公告名称',order: 1,view: 'text', type: 'string',},
  province: {title: '省份',order: 2,view: 'text', type: 'string',},
  city: {title: '城市',order: 3,view: 'text', type: 'string',},
  district: {title: '区县',order: 4,view: 'text', type: 'string',},
  address: {title: '详细地址',order: 5,view: 'text', type: 'string',},
  specialNotes: {title: '特殊说明',order: 6,view: 'text', type: 'string',},
  createTime: {title: '创建时间',order: 7,view: 'datetime', type: 'string',},
  updateTime: {title: '更新时间',order: 8,view: 'datetime', type: 'string',},
  createBy: {title: '创建人',order: 10,view: 'text', type: 'string',},
  updateBy: {title: '更新人',order: 11,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}