<template>
  <div class="p-4">
    <BasicTable @register="registerTable" @navigation-change="handleNavigationChange">
      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>

      <!-- 审核状态列 -->
      <template #reviewStatus="{ record }">
        <a-tag :color="getReviewStatusColor(record.review)">
          {{ getReviewStatusText(record.review) }}
        </a-tag>
      </template>

      <!-- 认证类型列 -->
      <template #authType="{ record }">
        <span>{{ record.authType || '-' }}</span>
      </template>

      <!-- 时间格式化 -->
      <template #submitTime="{ text }">
        <span v-if="text">{{ formatToDateTime(text) }}</span>
        <span v-else>-</span>
      </template>

      <!-- 审核时间格式化 -->
      <template #reviewTime="{ text }">
        <span v-if="text">{{ formatToDateTime(text) }}</span>
        <span v-else>-</span>
      </template>
    </BasicTable>

    <!-- 审核弹窗 -->
    <AuditModal v-model:open="auditVisible" :record="currentRecord" @success="handleAuditSuccess" />

    <!-- 详情查看弹窗 -->
    <DetailModal v-model:open="detailVisible" :record="currentRecord" />
  </div>
</template>

<script lang="ts" setup name="CertificationAudit">
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { queryPageAll, CertificationAuditRecord } from '/@/api/manageCenter/certificationAudit';
  import AuditModal from './components/AuditModal.vue';
  import DetailModal from './components/DetailModal.vue';

  const { createMessage } = useMessage();

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 120,
      customRender: ({ text }) => text || '-',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
      customRender: ({ text }) => text || '-',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
      customRender: ({ text }) => text || '-',
    },
    {
      title: '认证类型',
      dataIndex: 'authType',
      width: 100,
      slots: { customRender: 'authType' },
    },
    {
      title: '审核状态',
      dataIndex: 'review',
      width: 100,
      slots: { customRender: 'reviewStatus' },
    },
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      width: 160,
      slots: { customRender: 'submitTime' },
    },
    {
      title: '审核时间',
      dataIndex: 'reviewTime',
      width: 160,
      slots: { customRender: 'reviewTime' },
    },
  ];

  // 导航栏配置
  const navigationItems = [
    { key: 'all', label: '全部', icon: '' },
    { key: '1', label: '未审核', icon: '' },
    { key: '2', label: '已通过', icon: '' },
    { key: '3', label: '未通过', icon: '' },
  ];

  const activeNavigationKey = ref<string | number>('all');

  // 存储当前导航的查询参数
  const currentNavParams = ref<any>({});

  // 弹窗状态
  const auditVisible = ref(false);
  const detailVisible = ref(false);
  const currentRecord = ref<CertificationAuditRecord | null>(null);

  // 处理导航切换
  function handleNavigationChange(key: string | number) {
    activeNavigationKey.value = key;

    // 根据导航key设置不同的查询参数
    let searchParams = {};
    if (key !== 'all') {
      searchParams = {
        review: Number(key),
      };
    }

    // 存储导航参数
    currentNavParams.value = searchParams;
    // 重新加载数据
    reload();
  }

  // 自定义API调用函数
  async function customQueryPageAll(params: any) {
    // 合并导航参数和搜索表单参数
    const mergedParams = {
      ...params, // 搜索表单参数
      ...currentNavParams.value, // 添加导航参数
    };

    return queryPageAll(mergedParams);
  }

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: customQueryPageAll,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    // 导航栏配置
    showNavigation: true,
    navigationItems,
    activeNavigationKey: activeNavigationKey.value,
    inset: true,
    maxHeight: 480,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 80,
      size: 'large',
      schemas: [
        {
          field: 'phone',
          label: '手机号',
          component: 'Input',
          colProps: { span: 6 },
          componentProps: {
            placeholder: '请输入手机号',
          },
        },
        {
          field: 'name',
          label: '姓名',
          component: 'Input',
          colProps: { span: 6 },
          componentProps: {
            placeholder: '请输入姓名',
          },
        },
        {
          field: 'enterpriseName',
          label: '企业名称',
          component: 'Input',
          colProps: { span: 6 },
          componentProps: {
            placeholder: '请输入企业名称',
          },
        },
      ],
    },
  });

  // 操作按钮配置
  function getTableAction(record: CertificationAuditRecord): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '查看',
        onClick: handleViewDetail.bind(null, record),
      },
    ];

    // 只有未审核状态才显示审核按钮
    if (record.review === 1) {
      actions.unshift({
        label: '审核',
        onClick: handleAudit.bind(null, record),
      });
    }

    return actions;
  }

  // 处理审核
  function handleAudit(record: CertificationAuditRecord) {
    currentRecord.value = record;
    auditVisible.value = true;
  }

  // 处理查看详情
  function handleViewDetail(record: CertificationAuditRecord) {
    currentRecord.value = record;
    detailVisible.value = true;
  }

  // 审核成功回调
  function handleAuditSuccess() {
    auditVisible.value = false;
    currentRecord.value = null;
    reload();
    createMessage.success('审核操作成功');
  }

  // 获取审核状态颜色
  function getReviewStatusColor(status: number): string {
    switch (status) {
      case 1:
        return 'orange'; // 未审核
      case 2:
        return 'green'; // 已通过
      case 3:
        return 'red'; // 未通过
      default:
        return 'default';
    }
  }

  // 获取审核状态文本
  function getReviewStatusText(status: number): string {
    switch (status) {
      case 1:
        return '未审核';
      case 2:
        return '已通过';
      case 3:
        return '未通过';
      default:
        return '-';
    }
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
  }
</style>
