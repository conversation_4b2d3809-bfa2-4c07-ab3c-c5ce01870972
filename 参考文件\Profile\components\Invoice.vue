<template>
  <div class="invoice-container">
    <!-- 顶部导航切换 -->
    <div class="invoice-nav">
      <div
        class="nav-tab"
        :class="{ active: activeTab === 'enterprise' }"
        @click="switchTab('enterprise')"
      >
        企业认证
      </div>
      <div
        class="nav-tab"
        :class="{ active: activeTab === 'personal' }"
        @click="switchTab('personal')"
      >
        个人认证
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="invoice-content">
      <!-- 企业认证表单 -->
      <div v-if="activeTab === 'enterprise'" class="form-section">
        <!-- 企业名称 -->
        <div class="form-item">
          <label class="form-label">企业名称</label>
          <div class="form-value">
            <input
              v-model="enterpriseForm.companyName"
              type="text"
              class="form-input"
              placeholder="请输入企业名称"
            />
          </div>
        </div>
      </div>

      <!-- 个人认证表单 -->
      <div v-if="activeTab === 'personal'" class="form-section">
        <div class="empty-state">
          <p class="empty-text">个人认证功能开发中...</p>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <button class="save-btn" @click="handleSave">保存</button>
      <button class="submit-btn" @click="handleSubmit">提交审核</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";

// 当前激活的标签页
const activeTab = ref<"enterprise" | "personal">("enterprise");

// 企业认证表单数据
const enterpriseForm = reactive({
  companyName: "", // 企业名称
});

/**
 * 切换标签页
 * @param tab 标签页类型
 */
const switchTab = (tab: "enterprise" | "personal") => {
  activeTab.value = tab;
};

/**
 * 处理保存操作
 */
const handleSave = () => {
  // 这里可以添加保存逻辑
  ElMessage.success("保存成功");
};

/**
 * 处理提交审核操作
 */
const handleSubmit = () => {
  // 这里可以添加提交审核逻辑
  if (activeTab.value === "enterprise") {
    // 验证企业认证表单
    if (!enterpriseForm.companyName) {
      ElMessage.warning("请填写企业名称");
      return;
    }
  } else {
    ElMessage.info("个人认证功能开发中...");
    return;
  }

  ElMessage.success("提交审核成功");
};
</script>

<style scoped lang="scss">
.invoice-container {
  .invoice-nav {
    display: flex;
    height: 55px; // 导航区域高度
    width: 100%; // 宽度铺满页面
    border-radius: 10px;

    .nav-tab {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px; // 文字大小
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #eee; // 未选中状态背景色
      color: #999; // 未选中状态文字颜色

      &.active {
        background-color: #fff; // 选中状态背景色
        color: #004c66; // 选中状态字体颜色
      }

      &:hover:not(.active) {
        background-color: #e0e0e0; // 悬停效果
      }

      &:first-child {
        border-top-left-radius: 10px;
      }

      &:last-child {
        border-top-right-radius: 10px;
      }
    }
  }

  .invoice-content {
    padding: 20px;
    min-height: 680px; // 确保内容区域有足够高度
    position: relative;

    &::before {
      content: "";
      position: absolute;
      bottom: 20px;
      width: calc(100% - 40px);
      height: 1px;
      background: linear-gradient(
        to right,
        rgba(221, 221, 221, 1),
        rgba(255, 255, 255, 1)
      );
    }

    .form-section {
      .form-item {
        padding: 20px 0;
        border-bottom: 1px solid;
        // 渐变
        border-image: linear-gradient(
          to right,
          rgba(221, 221, 221, 1),
          rgba(255, 255, 255, 1)
        );
        border-image-slice: 1;

        .form-label {
          width: 120px;
          font-size: 16px;
          color: #333;
          flex-shrink: 0;
          font-family: 'PingFang Bold';
        }

        .form-value {
          margin-top: 10px;
          flex: 1;

          .form-input {
            width: 100%;
            border: none; // 输入框没有边框
            outline: none;
            font-size: 16px;
            color: #666; // 输入框文字颜色
            background: transparent;
            transition: border-color 0.3s ease;

            &::placeholder {
              color: #999; // 占位符颜色，和个人资料页面的value样式一样
              font-size: 16px;
            }

            &:focus {
              border-bottom-color: #004c66; // 聚焦时底部边框颜色
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;

      .empty-text {
        font-size: 16px;
        color: #999;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    padding: 0 20px 20px 20px;

    .save-btn,
    .submit-btn {
      padding: 9px 24px;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;
    }

    .save-btn {
      background-color: rgba(0, 76, 102, 0.2); // 保存按钮背景色
      color: #004c66; // 保存按钮字体颜色

      &:hover {
        background-color: rgba(0, 76, 102, 0.3);
      }
    }

    .submit-btn {
      background-color: #004c66; // 提交审核按钮背景色
      color: #fff; // 提交审核按钮字体颜色

      &:hover {
        background-color: #003a4d;
      }
    }
  }
}
</style>
